defmodule Drops.Relation.Plugins.Ecto.Query do
  @moduledoc """
  Plugin for defining custom Ecto queries within relation modules.

  This plugin provides the `defquery` macro that allows defining custom query functions
  that return relation structs with the queryable set to the result of the query block.

  ## Architecture

  For each relation module that defines queries, this plugin creates a dedicated
  QueryBuilder module (e.g., `MyRelation.QueryBuilder`) that contains the actual
  query functions with `Ecto.Query` imported once at the module level. The relation
  module then delegates to these QueryBuilder functions.

  This approach avoids importing `Ecto.Query` in each generated function and provides
  a clean separation of concerns.

  ## Usage

      relation(:users) do
        schema("users", infer: true)

        defquery active() do
          from(u in relation(), where: u.active == true)
        end
      end

      # This creates:
      # - users.active() -> delegates to users.QueryBuilder.active()
      # - users.QueryBuilder module with Ecto.Query imported
      # - relation() function available in query blocks

  The `relation()` function within the query block returns the relation module.
  """

  use Drops.Relation.Plugin, imports: [defquery: 2]

  defmacro defquery(call, do: block) do
    {name, args} = Macro.decompose_call(call)

    # Extract variable names from args to avoid variable binding issues
    arg_names =
      Enum.map(args, fn
        {var_name, _, _} when is_atom(var_name) -> var_name
        var_name when is_atom(var_name) -> var_name
      end)

    quote do
      @context update_context(__MODULE__, :queries, [
                 unquote(name),
                 unquote(arg_names),
                 unquote(Macro.escape(block))
               ])
    end
  end

  def on(:before_compile, relation, _) do
    queries = context(relation, :queries) || []

    if queries != [] do
      # Generate delegation functions
      delegation_functions =
        Enum.map(queries, fn query ->
          generate_delegation_function(relation, query)
        end)

      quote do
        (unquote_splicing(delegation_functions))
      end
    else
      quote do: nil
    end
  end

  def on(:after_compile, relation, _) do
    queries = context(relation, :queries) || []

    if queries != [] do
      quote location: :keep do
        unquote(__MODULE__).create_query_builder_module(
          unquote(relation),
          unquote(Macro.escape(queries))
        )
      end
    else
      quote do: nil
    end
  end

  def create_query_builder_module(relation, queries) do
    query_builder_module = query_builder_module_name(relation)

    query_functions =
      Enum.map(queries, fn query ->
        generate_query_function(relation, query)
      end)

    module_code =
      quote do
        import Ecto.Query

        def relation(), do: unquote(relation)

        (unquote_splicing(query_functions))
      end

    Module.create(
      query_builder_module,
      module_code,
      Macro.Env.location(__ENV__)
    )
  end

  defp generate_delegation_function(relation, %{name: name, args: args}) do
    query_builder_module = query_builder_module_name(relation)

    # args are now already atom names, not AST nodes
    arg_names = args

    # Create proper variable AST nodes for the function definition and call
    param_vars = Enum.map(arg_names, &Macro.var(&1, nil))

    quote do
      def unquote({name, [], param_vars}) do
        queryable = unquote(query_builder_module).unquote(name)(unquote_splicing(param_vars))
        new(queryable, [])
      end
    end
  end

  defp generate_query_function(_relation, %{name: name, args: args, block: block}) do
    # args are now already atom names, not AST nodes
    arg_names = args

    # Create proper variable AST nodes for the function definition
    param_vars = Enum.map(arg_names, &Macro.var(&1, nil))

    # The key fix: replace variables in the escaped block with proper function parameter variables
    updated_block = replace_variables_in_block(block, arg_names)

    quote do
      def unquote({name, [], param_vars}) do
        unquote(updated_block)
      end
    end
  end

  # Replace variables in the block AST with proper function parameter variables
  defp replace_variables_in_block(block, arg_names) do
    Macro.postwalk(block, fn
      {var_name, _meta, _context} = var_ast when is_atom(var_name) ->
        if var_name in arg_names do
          # Replace with a proper variable reference bound to function parameter
          # This handles both nil context (already bound) and Elixir context (unbound)
          Macro.var(var_name, nil)
        else
          var_ast
        end

      other ->
        other
    end)
  end

  defp query_builder_module_name(relation) do
    Module.concat([relation, QueryBuilder])
  end
end
