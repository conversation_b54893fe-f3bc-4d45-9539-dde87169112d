defmodule Drops.Relations.Plugins.Ecto.QueryTest do
  use Drops.RelationCase, async: false

  describe "query/1 with no args" do
    relation(:users) do
      schema("users", infer: true)

      defquery active() do
        from(u in relation(), where: u.active == true)
      end
    end

    test "defines a custom query accessible via relation module", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      assert [%{name: "<PERSON>"}] = users.active() |> Enum.to_list()
    end

    test "composes with relations", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})

      {:ok, jade} = users.insert(%{name: "<PERSON>", active: true})

      assert [%{name: "<PERSON>"} = user] =
               users.active() |> users.get_by_name("<PERSON>") |> Enum.to_list()

      assert jade.id == user.id
    end
  end

  describe "query/1 with args" do
    relation(:users) do
      schema("users", infer: true)

      defquery by_name(name) do
        from(u in relation(), where: u.name == ^name)
      end

      defquery by_age(age) do
        from(u in relation(), where: u.age == ^age)
      end
    end

    test "defines a custom query with args", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false, age: 31})
      users.insert(%{name: "Jane", active: true, age: 42})

      assert [%{name: "John"}] = users.by_name("John") |> Enum.to_list()
      assert [%{name: "Jane"}] = users.by_name("Jane") |> Enum.to_list()
    end

    test "composes multiple queries", %{users: users} do
      users.insert(%{name: "John", active: false, age: 31})
      users.insert(%{name: "Jane", active: false, age: 42})
      users.insert(%{name: "John", active: true, age: 42})

      assert [%{name: "John", age: 42}] =
               users.by_name("John") |> users.by_age(42) |> Enum.to_list()
    end
  end
end
